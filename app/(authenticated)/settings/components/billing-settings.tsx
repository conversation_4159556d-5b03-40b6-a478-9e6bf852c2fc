"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CreditCard, AlertCircle, Loader2, ExternalLink } from "lucide-react"
import {
  useIsUserSubscribed,
  useUserSubscriptionPlan,
  useUserSubscriptionStatus,
  useUserSubscriptionDetails,
  useUserSubscriptionLoading,
} from "@/lib/domains/user-subscription/user-subscription.hooks"
import { toast } from "@/components/ui/use-toast"
import {
  createCheckoutSession,
  createCustomerPortalSession,
  PLANS,
  SUBSCRIPTION_PRICES,
  formatDate,
} from "@/lib/stripe-client"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"

export function BillingSettings() {
  // Get subscription data from domain hooks
  const isSubscribed = useIsUserSubscribed()
  const subscriptionPlan = useUserSubscriptionPlan()
  const subscriptionStatus = useUserSubscriptionStatus()
  const subscription = useUserSubscriptionDetails()
  const subscriptionLoading = useUserSubscriptionLoading()
  const [loading, setLoading] = useState(false)
  const [billingPeriod, setBillingPeriod] = useState<"monthly" | "yearly">("monthly")

  /**
   * Initiates the subscription upgrade process by creating a Stripe checkout session
   *
   * For testing, you can use these test card numbers:
   * - 4242 4242 4242 4242: Successful payment
   * - 4000 0000 0000 0002: Card declined
   * - 4000 0000 0000 0341: Subscription initially succeeds but fails on renewal
   *
   * See docs/stripe-testing.md for more test card numbers and scenarios.
   */
  const upgradeAccount = async () => {
    try {
      setLoading(true)
      const priceId = billingPeriod === "monthly" ? PLANS.MONTHLY : PLANS.YEARLY
      const { url } = await createCheckoutSession(priceId)
      window.location.href = url
    } catch (error) {
      console.error("Error upgrading account:", error)
      toast({
        title: "Error",
        description: "Failed to start subscription process. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  /**
   * Opens the Stripe customer portal to manage subscription, payment methods, and billing history
   */
  const manageSubscription = async () => {
    // Don't proceed if user doesn't have an active subscription
    if (!isSubscribed) {
      toast({
        title: "Subscription error",
        description: "You don't have an active subscription to manage.",
        variant: "destructive",
      })
      return
    }

    try {
      setLoading(true)
      const { url } = await createCustomerPortalSession()
      window.location.href = url
    } catch (error) {
      console.error("Error managing subscription:", error)

      // Show a more specific error message
      let errorMessage = "Failed to open subscription management. Please try again."

      // Check for specific error types
      if (error instanceof Error) {
        if (error.message.includes("User does not have a Stripe customer ID")) {
          errorMessage = "You don't have an active subscription to manage."
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Render skeleton loading state while subscription data is loading
  if (subscriptionLoading) {
    console.log("Subscription is loading, showing skeleton")
    return <BillingSettingsSkeleton />
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Subscription</CardTitle>
          <CardDescription>Manage your subscription plan</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Plan */}
          <div className="rounded-lg border p-4">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-semibold">
                  {isSubscribed ? "Pro Plan" : "Free Plan"}
                  {subscriptionPlan === "yearly" && " (Annual)"}
                  {subscriptionPlan === "monthly" && " (Monthly)"}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {isSubscribed
                    ? "Advanced features for frequent travelers"
                    : "Basic features for personal use"}
                </p>
                {isSubscribed && (
                  <>
                    <p className="text-sm mt-1">Status: {subscriptionStatus || "active"}</p>
                    {subscription?.subscriptionCurrentPeriodEnd && (
                      <p className="text-sm mt-1">
                        Renews on {formatDate(subscription.subscriptionCurrentPeriodEnd)}
                      </p>
                    )}
                    {/* Add debug info for troubleshooting */}
                    {process.env.NODE_ENV === "development" &&
                      subscription?.subscriptionCurrentPeriodEnd && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Debug: {JSON.stringify(subscription.subscriptionCurrentPeriodEnd)}
                        </p>
                      )}
                  </>
                )}
              </div>
              <Badge>{isSubscribed ? "Pro" : "Free"}</Badge>
            </div>

            <div className="mt-4 space-y-2">
              {isSubscribed ? (
                <>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-primary" />
                    <span className="text-sm">Up to 5 squads</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-primary" />
                    <span className="text-sm">Up to 3 trips per squad</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-primary" />
                    <span className="text-sm">Unlimited AI requests</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-primary" />
                    <span className="text-sm">Trip chat messaging</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-primary" />
                    <span className="text-sm">Advanced trip planning</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-primary" />
                    <span className="text-sm">Priority support</span>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-primary" />
                    <span className="text-sm">Up to 1 squad</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-secondary" />
                    <span className="text-sm text-muted-foreground">+1 squad coming soon</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-primary" />
                    <span className="text-sm">Up to 2 trips per squad</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-primary" />
                    <span className="text-sm">Limited to 3 AI requests</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Trip chat (view only)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-primary" />
                    <span className="text-sm">Basic trip planning</span>
                  </div>
                </>
              )}
            </div>

            {isSubscribed && (
              <div className="mt-4">
                <Button
                  variant="outline"
                  onClick={manageSubscription}
                  disabled={loading}
                  className="w-full"
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Manage Subscription
                </Button>
              </div>
            )}
          </div>

          {/* Upgrade Options (only show if not subscribed) */}
          {!isSubscribed && (
            <>
              <h3 className="font-medium text-lg mt-6">Upgrade to Pro</h3>

              <Tabs
                defaultValue="monthly"
                className="w-full"
                onValueChange={(v) => setBillingPeriod(v as "monthly" | "yearly")}
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="monthly">Monthly</TabsTrigger>
                  <TabsTrigger value="yearly">Yearly (Save 17%)</TabsTrigger>
                </TabsList>

                <TabsContent value="monthly" className="mt-4">
                  <div className="rounded-lg border p-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-semibold">Pro Plan (Monthly)</h3>
                        <p className="text-sm text-muted-foreground">
                          Advanced features for frequent travelers
                        </p>
                        <p className="text-sm font-medium mt-1">
                          ${SUBSCRIPTION_PRICES.MONTHLY}/month
                        </p>
                      </div>
                      <Button onClick={upgradeAccount} disabled={loading}>
                        {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        Upgrade
                      </Button>
                    </div>

                    <div className="mt-4 space-y-2">
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Up to 5 squads</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Up to 3 trips per squad</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Unlimited AI requests</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Trip chat messaging</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Advanced trip planning</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Priority support</span>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="yearly" className="mt-4">
                  <div className="rounded-lg border p-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-semibold">Pro Plan (Yearly)</h3>
                        <p className="text-sm text-muted-foreground">
                          Advanced features for frequent travelers
                        </p>
                        <p className="text-sm font-medium mt-1">
                          ${SUBSCRIPTION_PRICES.YEARLY}/year
                        </p>
                        <p className="text-xs text-green-600 mt-1">Save 17% compared to monthly</p>
                      </div>
                      <Button onClick={upgradeAccount} disabled={loading}>
                        {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        Upgrade
                      </Button>
                    </div>

                    <div className="mt-4 space-y-2">
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Unlimited squads</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Unlimited trips per squad</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Unlimited AI requests</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Trip chat messaging</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Star badge next to your name</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Advanced trip planning</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Priority support</span>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </>
          )}
        </CardContent>
      </Card>

      {isSubscribed && (
        <Card>
          <CardHeader>
            <CardTitle>Payment Methods</CardTitle>
            <CardDescription>Manage your payment methods</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <CreditCard className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="font-medium">Payment method on file</p>
                  <p className="text-sm text-muted-foreground">
                    Manage your payment methods in the Stripe customer portal
                  </p>
                </div>
              </div>
              <Button variant="outline" onClick={manageSubscription} disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Manage
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>View your past invoices</CardDescription>
        </CardHeader>
        <CardContent>
          {isSubscribed ? (
            <div className="space-y-4">
              <p className="text-sm">
                Access your complete billing history and download invoices from the Stripe customer
                portal.
              </p>
              <Button
                variant="outline"
                onClick={manageSubscription}
                disabled={loading}
                className="flex items-center"
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                View Billing History
                <ExternalLink className="ml-2 h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-6 text-center">
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <AlertCircle className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">No billing history</h3>
              <p className="text-sm text-muted-foreground">
                Your billing history will appear here once you upgrade to a paid plan.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  )
}

// Helper component for checkmarks
function CheckIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <polyline points="20 6 9 17 4 12" />
    </svg>
  )
}

// Skeleton loading component for billing settings
function BillingSettingsSkeleton() {
  return (
    <>
      <Card>
        <CardHeader>
          <Skeleton className="h-7 w-40" />
          <Skeleton className="h-5 w-60 mt-1.5" />
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Plan Skeleton */}
          <div className="rounded-lg border p-4">
            <div className="flex items-start justify-between">
              <div>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48 mt-1" />
                <Skeleton className="h-4 w-36 mt-2" />
              </div>
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>

            <div className="mt-4 space-y-2">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-40" />
                </div>
              ))}
            </div>
          </div>

          {/* Upgrade Options Skeleton */}
          <Skeleton className="h-6 w-32 mt-6" />
          <div className="mt-4">
            <Skeleton className="h-10 w-full mb-4" />
            <div className="rounded-lg border p-4">
              <div className="flex items-start justify-between">
                <div>
                  <Skeleton className="h-6 w-40" />
                  <Skeleton className="h-4 w-56 mt-1" />
                  <Skeleton className="h-4 w-24 mt-1" />
                </div>
                <Skeleton className="h-9 w-24" />
              </div>

              <div className="mt-4 space-y-2">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <div key={i} className="flex items-center gap-2">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-40" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Methods Skeleton */}
      <Card className="mt-6">
        <CardHeader>
          <Skeleton className="h-7 w-48" />
          <Skeleton className="h-5 w-60 mt-1.5" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div>
                <Skeleton className="h-5 w-40" />
                <Skeleton className="h-4 w-56 mt-1" />
              </div>
            </div>
            <Skeleton className="h-9 w-24" />
          </div>
        </CardContent>
      </Card>

      {/* Billing History Skeleton */}
      <Card className="mt-6">
        <CardHeader>
          <Skeleton className="h-7 w-36" />
          <Skeleton className="h-5 w-48 mt-1.5" />
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <Skeleton className="h-12 w-12 rounded-full mb-4" />
            <Skeleton className="h-6 w-40 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
        </CardContent>
      </Card>
    </>
  )
}
